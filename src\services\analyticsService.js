// ===================================
// ANALYTICS SERVICE - SERVICE LAYER
// ===================================
// Revenue analytics, appointment trends, customer behavior, staff metrics, business insights

const Appointment = require("../models/appointment");
const Invoice = require("../models/invoice");
const Customer = require("../models/customer");
const Staff = require("../models/staff");
const Salon = require("../models/salon");
const Service = require("../models/service");
const WalletTransaction = require("../models/walletTransaction");

const { startOfDay, endOfDay, addDays, subDays } = require("date-fns");

class AnalyticsService {
  // ==============================
  // REVENUE ANALYTICS
  // ==============================

  async getDashboardRevenue(salonId, startDate = null, endDate = null) {
    const query = { salonId };
    if (startDate && endDate) {
      query.createdAt = { $gte: startDate, $lte: endDate };
    }

    const totalRevenue = await Invoice.aggregate([
      { $match: query },
      { $group: { _id: null, totalRevenue: { $sum: "$amount" } } },
    ]);

    const dailyRevenue = await Invoice.aggregate([
      { $match: query },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          revenue: { $sum: "$amount" },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return {
      totalRevenue: totalRevenue[0]?.totalRevenue || 0,
      dailyRevenue,
    };
  }

  async getRevenueByService(salonId, startDate = null, endDate = null) {
    const query = { salonId };
    if (startDate && endDate) query.createdAt = { $gte: startDate, $lte: endDate };

    const revenueByService = await Appointment.aggregate([
      { $match: query },
      { $unwind: "$serviceIds" },
      { $group: { _id: "$serviceIds", totalRevenue: { $sum: "$totalAmount" }, count: { $sum: 1 } } },
      { $lookup: { from: "services", localField: "_id", foreignField: "_id", as: "service" } },
      { $unwind: "$service" },
      { $project: { serviceName: "$service.name", totalRevenue: 1, count: 1 } },
      { $sort: { totalRevenue: -1 } },
    ]);

    return revenueByService;
  }

  async getRevenueByStaff(salonId, startDate = null, endDate = null) {
    const query = { salonId };
    if (startDate && endDate) query.createdAt = { $gte: startDate, $lte: endDate };

    const revenueByStaff = await Appointment.aggregate([
      { $match: query },
      { $group: { _id: "$staffId", totalRevenue: { $sum: "$totalAmount" }, appointments: { $sum: 1 } } },
      { $lookup: { from: "staffs", localField: "_id", foreignField: "_id", as: "staff" } },
      { $unwind: "$staff" },
      { $project: { staffName: "$staff.name", totalRevenue: 1, appointments: 1 } },
      { $sort: { totalRevenue: -1 } },
    ]);

    return revenueByStaff;
  }

  // ==============================
  // APPOINTMENT ANALYTICS
  // ==============================

  async getAppointmentTrends(salonId, startDate = null, endDate = null) {
    const query = { salonId };
    if (startDate && endDate) query.scheduledAt = { $gte: startDate, $lte: endDate };

    const totalAppointments = await Appointment.countDocuments(query);
    const completed = await Appointment.countDocuments({ ...query, status: "completed" });
    const cancelled = await Appointment.countDocuments({ ...query, status: "cancelled" });
    const noShows = await Appointment.countDocuments({ ...query, status: "no-show" });

    return { totalAppointments, completed, cancelled, noShows };
  }

  async getServicePopularity(salonId, startDate = null, endDate = null) {
    const query = { salonId };
    if (startDate && endDate) query.scheduledAt = { $gte: startDate, $lte: endDate };

    const services = await Appointment.aggregate([
      { $match: query },
      { $unwind: "$serviceIds" },
      { $group: { _id: "$serviceIds", count: { $sum: 1 } } },
      { $lookup: { from: "services", localField: "_id", foreignField: "_id", as: "service" } },
      { $unwind: "$service" },
      { $project: { serviceName: "$service.name", count: 1 } },
      { $sort: { count: -1 } },
    ]);

    return services;
  }

  // ==============================
  // CUSTOMER ANALYTICS
  // ==============================

  async getCustomerRetentionMetrics(salonId) {
    const totalCustomers = await Customer.countDocuments({ salonId });
    const returningCustomers = await Appointment.distinct("customerId", { salonId });

    return {
      totalCustomers,
      returningCustomers: returningCustomers.length,
      retentionRate: totalCustomers > 0 ? ((returningCustomers.length / totalCustomers) * 100).toFixed(2) : 0,
    };
  }

  async getCustomerVisitFrequency(salonId) {
    const visits = await Appointment.aggregate([
      { $match: { salonId } },
      { $group: { _id: "$customerId", visits: { $sum: 1 } } },
      { $sort: { visits: -1 } },
    ]);

    return visits;
  }

  // ==============================
  // STAFF PERFORMANCE ANALYTICS
  // ==============================

  async getStaffProductivity(salonId) {
    const productivity = await Appointment.aggregate([
      { $match: { salonId, status: "completed" } },
      { $group: { _id: "$staffId", completedAppointments: { $sum: 1 } } },
      { $lookup: { from: "staffs", localField: "_id", foreignField: "_id", as: "staff" } },
      { $unwind: "$staff" },
      { $project: { staffName: "$staff.name", completedAppointments: 1 } },
    ]);

    return productivity;
  }

  async getStaffEfficiencyMetrics(salonId) {
    // Placeholder for metrics like average service time vs scheduled time
    return [];
  }

  // ==============================
  // REPORT GENERATION
  // ==============================

  async generateDashboardData(salonId) {
    const revenue = await this.getDashboardRevenue(salonId);
    const trends = await this.getAppointmentTrends(salonId);
    const topServices = await this.getServicePopularity(salonId);

    return { revenue, trends, topServices };
  }

  async generateCustomReport(salonId, reportType, filters) {
    // Placeholder: filter data based on reportType (revenue, appointments, customers)
    return {};
  }
}

// Export service instance
module.exports = new AnalyticsService();
