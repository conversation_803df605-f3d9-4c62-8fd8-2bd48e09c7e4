# ===============================================
# SALON/SPA BACKEND - GIT IGNORE FILE
# ===============================================
# This file specifies which files and directories should be ignored by Git

# ===============================================
# NODE.JS DEPENDENCIES
# ===============================================
# Node modules directory (contains all npm packages)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# ===============================================
# ENVIRONMENT AND CONFIGURATION FILES
# ===============================================
# Environment variables (contains sensitive data like API keys, passwords)
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Configuration files with sensitive data
config/production.json
config/staging.json
config/development.json
config/local.json

# ===============================================
# LOGS AND DEBUG FILES
# ===============================================
# Application logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ===============================================
# DATABASE FILES
# ===============================================
# SQLite database files
*.db
*.sqlite
*.sqlite3

# Database dump files
*.sql
*.dump
backup/
dumps/

# ===============================================
# CACHE AND TEMPORARY FILES
# ===============================================
# Dependency directories
.npm
.yarn-integrity

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Temporary folders
tmp/
temp/
.tmp/
.temp/

# ===============================================
# BUILD AND DISTRIBUTION FILES
# ===============================================
# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript compiled files
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# ===============================================
# EDITOR AND IDE FILES
# ===============================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# WebStorm / IntelliJ
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*.swn

# Emacs
*~
\#*\#
.\#*

# ===============================================
# OPERATING SYSTEM FILES
# ===============================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===============================================
# SECURITY AND SENSITIVE FILES
# ===============================================
# SSL certificates and private keys
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx
certificates/
ssl/

# SSH keys
*.rsa
*.dsa
id_rsa*
id_dsa*

# API keys and secrets
secrets/
.secrets/
api-keys/
credentials/

# JWT tokens and session files
*.jwt
sessions/

# ===============================================
# FILE UPLOADS AND MEDIA
# ===============================================
# User uploaded files
uploads/
public/uploads/
storage/
media/
files/

# Images and media cache
.image-cache/
thumbnails/
resized/

# ===============================================
# DOCUMENTATION AND REPORTS
# ===============================================
# Generated documentation
docs/generated/
api-docs/generated/

# Coverage reports
coverage/
.nyc_output
*.lcov

# Test reports
test-results/
junit.xml

# ===============================================
# DOCKER AND CONTAINERIZATION
# ===============================================
# Docker files (optional - you might want to include these)
# Dockerfile
# docker-compose.yml
# .dockerignore

# ===============================================
# CLOUD AND DEPLOYMENT
# ===============================================
# Heroku
.heroku/

# AWS
.aws/
.elasticbeanstalk/

# Google Cloud
.gcloud/

# Azure
.azure/

# Serverless
.serverless/

# ===============================================
# PACKAGE MANAGERS
# ===============================================
# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# ===============================================
# TESTING
# ===============================================
# Jest
jest_coverage/

# Mocha
.mocha/

# Test databases
test.db
test.sqlite

# ===============================================
# LINTING AND FORMATTING
# ===============================================
# ESLint
.eslintignore

# Prettier
.prettierignore

# ===============================================
# MISCELLANEOUS
# ===============================================
# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Lock files (optional - you might want to include package-lock.json)
# package-lock.json
# yarn.lock

# Runtime files
*.pid
*.seed
*.pid.lock

# Optional: Include these if you want to track them
# README.md
# LICENSE
# CHANGELOG.md

# ===============================================
# PROJECT SPECIFIC IGNORES
# ===============================================
# Salon/Spa specific files to ignore

# Customer data exports
exports/customers/
exports/appointments/
exports/billing/

# Reports and analytics exports
reports/
analytics-data/

# Backup salon data
salon-backups/

# Generated invoices and receipts
generated-invoices/
receipts/

# Staff photos and documents
staff-photos/
staff-documents/

# Salon branding and customization files
branding/
custom-themes/

# Third-party integration configs
integrations/configs/

# Notification templates with sensitive data
notification-templates/production/

