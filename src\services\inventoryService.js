// ======================================
// INVENTORY SERVICE - SERVICE LAYER
// ======================================

const Inventory = require("../models/inventory");
const Supplier = require("../models/supplier");
const PurchaseOrder = require("../models/purchaseOrder");
const InventoryMovement = require("../models/inventoryMovement");
const Service = require("../models/service");
const Appointment = require("../models/appointment");

// Utilities
const { format } = require("date-fns");

class InventoryService {
  // ==============================
  // INVENTORY ITEM MANAGEMENT
  // ==============================
  
  async getInventory(salonId, filters = {}, page = 1, limit = 10, search = null) {
    const query = { salonId, ...filters };

    if (search) {
      const searchRegex = { $regex: search, $options: "i" };
      query.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { category: searchRegex },
      ];
    }

    const items = await Inventory.find(query)
      .populate("supplierId", "name contact email")
      .sort({ updatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Inventory.countDocuments(query);

    return {
      items,
      meta: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        total,
        hasNextPage: page * limit < total,
        hasPrevPage: page > 1,
      },
    };
  }

  async getInventoryById(itemId, salonId) {
    return await Inventory.findOne({ _id: itemId, salonId })
      .populate("supplierId")
      .populate("movements")
      .populate("servicesUsed");
  }

  async createInventory(data, salonId) {
    const inventory = new Inventory({ ...data, salonId });
    // TODO: generate barcode/item code
    await inventory.save();
    this.sendNotification(`Inventory item created: ${inventory.name}`);
    return inventory;
  }

  async updateInventory(itemId, updateData, salonId) {
    const updated = await Inventory.findOneAndUpdate(
      { _id: itemId, salonId },
      updateData,
      { new: true }
    );
    this.sendNotification(`Inventory item updated: ${updated.name}`);
    return updated;
  }

  async deleteInventory(itemId, salonId) {
    const deleted = await Inventory.findOneAndDelete({ _id: itemId, salonId });
    this.sendNotification(`Inventory item deleted: ${deleted?.name || itemId}`);
    return deleted;
  }

  // ==============================
  // STOCK LEVEL MANAGEMENT
  // ==============================

  async updateStock(itemId, quantity, type, reason, salonId, userId) {
    const item = await Inventory.findOne({ _id: itemId, salonId });
    if (!item) throw new Error("Inventory item not found");

    const change = type === "add" ? quantity : -quantity;
    item.stockLevel += change;
    await item.save();

    const movement = new InventoryMovement({
      itemId,
      salonId,
      quantity: change,
      type,
      reason,
      userId,
      createdAt: new Date(),
    });
    await movement.save();

    this.sendNotification(
      `Stock updated for ${item.name}: ${type} ${quantity} units`
    );
    return movement;
  }

  async adjustStockLevels(adjustments, salonId, userId) {
    const results = [];
    for (const adj of adjustments) {
      const movement = await this.updateStock(
        adj.itemId,
        adj.quantity,
        adj.type,
        adj.reason,
        salonId,
        userId
      );
      results.push(movement);
    }
    return results;
  }

  async performStockCount(salonId, countData, userId) {
    const adjustments = [];
    for (const { itemId, physicalCount } of countData) {
      const item = await Inventory.findOne({ _id: itemId, salonId });
      if (!item) continue;
      const diff = physicalCount - item.stockLevel;
      if (diff !== 0) {
        const movement = await this.updateStock(
          itemId,
          Math.abs(diff),
          diff > 0 ? "add" : "remove",
          "Stock count adjustment",
          salonId,
          userId
        );
        adjustments.push(movement);
      }
    }
    return adjustments;
  }

  async setReorderPoints(itemId, minStock, maxStock, reorderQty) {
    return await Inventory.findByIdAndUpdate(
      itemId,
      { minStock, maxStock, reorderQuantity: reorderQty },
      { new: true }
    );
  }

  // ==============================
  // SUPPLIER & PURCHASE ORDERS
  // ==============================

  async manageSuppliers(salonId, supplierData) {
    const supplier = new Supplier({ ...supplierData, salonId });
    await supplier.save();
    this.sendNotification(`Supplier added: ${supplier.name}`);
    return supplier;
  }

  async createPurchaseOrder(salonId, orderData, userId) {
    const po = new PurchaseOrder({
      ...orderData,
      salonId,
      createdBy: userId,
      status: "pending",
      createdAt: new Date(),
    });
    await po.save();
    this.sendNotification(`Purchase order created: ${po._id}`);
    return po;
  }

  async receivePurchaseOrder(orderId, receivingData, userId) {
    const po = await PurchaseOrder.findById(orderId);
    if (!po) throw new Error("Purchase order not found");

    po.status = "received";
    await po.save();

    for (const { itemId, quantity } of receivingData) {
      await this.updateStock(itemId, quantity, "add", "Purchase received", po.salonId, userId);
    }

    this.sendNotification(`Purchase order received: ${po._id}`);
    return po;
  }

  // ==============================
  // PRODUCT USAGE TRACKING
  // ==============================

  async trackServiceUsage(appointmentId, serviceId, usedProducts) {
    for (const { itemId, quantity } of usedProducts) {
      await this.updateStock(itemId, quantity, "remove", `Used in service ${serviceId}`, null, null);
    }
    this.sendNotification(`Products used for service ${serviceId} in appointment ${appointmentId}`);
    return usedProducts;
  }

  async calculateServiceCosts(serviceId, salonId) {
    const services = await Service.findById(serviceId).populate("productsUsed");
    let totalCost = 0;
    services.productsUsed.forEach(prod => totalCost += prod.price);
    return { serviceId, totalCost };
  }

  async analyzeProductUsage(salonId, period = {}) {
    // Example: total quantity used per product
    const pipeline = [
      { $match: { salonId, createdAt: { $gte: period.start, $lte: period.end } } },
      { $group: { _id: "$itemId", totalUsed: { $sum: "$quantity" } } }
    ];
    const usage = await InventoryMovement.aggregate(pipeline);
    return usage;
  }

  // ==============================
  // NOTIFICATIONS
  // ==============================
  
  sendNotification(message) {
    console.log(`Inventory Notification: ${message}`);
  }

  // ==============================
  // ANALYTICS
  // ==============================

  async getInventoryAnalytics(salonId) {
    const totalItems = await Inventory.countDocuments({ salonId });
    const lowStock = await Inventory.countDocuments({ salonId, stockLevel: { $lte: 10 } });
    return { totalItems, lowStock };
  }
}

// Export service instance
module.exports = new InventoryService();
