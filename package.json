{"name": "salon-spa-saas-backend", "version": "1.0.0", "description": "Complete SaaS backend for Salon/Spa management with Super Admin and Salon Admin portals", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js", "migrate": "node scripts/migrate.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "winston": "^3.9.0", "express-rate-limit": "^6.8.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "moment": "^2.29.4", "helmet": "^7.0.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "mongoose-paginate-v2": "^1.7.4", "uuid": "^9.0.0", "sharp": "^0.32.5", "pdfkit": "^0.13.0", "exceljs": "^4.4.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "redis": "^4.6.7", "bull": "^4.11.3", "socket.io": "^4.7.2", "firebase-admin": "^13.4.0", "stripe": "^12.18.0", "razorpay": "^2.9.2", "aws-sdk": "^2.1419.0", "cloudinary": "^1.40.0", "handlebars": "^4.7.8", "joi": "^17.9.2", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "passport": "^0.6.0", "passport-local": "^1.0.0", "passport-google-oauth20": "^2.0.0", "morgan": "^1.10.0", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4", "hpp": "^0.2.3", "express-slow-down": "^1.6.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "node-geocoder": "^4.2.0", "agenda": "^5.0.0", "dayjs": "^1.11.9", "lodash": "^4.17.21", "image-size": "^1.0.2", "mime-types": "^2.1.35", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "yamljs": "^0.3.0", "axios": "^1.5.0", "cheerio": "^1.0.0-rc.12", "xml2js": "^0.6.2", "fast-csv": "^4.3.6", "archiver": "^6.0.1", "extract-zip": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.0", "prettier": "^3.0.1", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^15.2.10", "cross-env": "^7.0.3", "concurrently": "^8.2.0", "mongodb-memory-server": "^8.15.1", "@types/node": "^20.4.9", "nyc": "^15.1.0", "faker": "^5.5.3", "sinon": "^15.2.0", "chai": "^4.3.7", "mocha": "^10.2.0", "standard-version": "^9.5.0", "@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "jsdoc": "^4.0.2", "madge": "^6.1.0", "snyk": "^1.1193.0", "audit-ci": "^6.6.1", "license-checker": "^25.0.1", "nock": "^13.3.2", "rewire": "^7.0.0", "proxyquire": "^2.1.3", "istanbul": "^0.4.5"}}