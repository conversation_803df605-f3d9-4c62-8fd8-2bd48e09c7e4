// ======================================
// APPOINTMENT SERVICE - SERVICE LAYER
// ======================================
const Appointment = require("../models/appointment");
const Staff = require("../models/staff");
const Customer = require("../models/customer");
const Service = require("../models/service");

// Utility for date/time handling
const { isAfter, isBefore, addMinutes, format } = require("date-fns");

class AppointmentService {
  // ==============================
  // APPOINTMENT RETRIEVAL
  // ==============================
  
  async getAppointments(salonId, filters = {}, page = 1, limit = 10) {
    const query = { tenantId: salonId, ...filters };
    const appointments = await Appointment.find(query)
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds")
      .sort({ scheduledAt: 1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Appointment.countDocuments(query);

    return {
      appointments,
      meta: { page, limit, totalPages: Math.ceil(total / limit), total },
    };
  }

  async getAppointmentById(appointmentId, salonId) {
    return await Appointment.findOne({ _id: appointmentId, tenantId: salonId })
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds");
  }

  async getAppointmentsByDateRange(salonId, startDate, endDate) {
    return await Appointment.find({
      tenantId: salonId,
      scheduledAt: { $gte: startDate, $lte: endDate },
    })
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds")
      .sort({ scheduledAt: 1 });
  }

  async getTodaysAppointments(salonId) {
    const today = new Date();
    const start = new Date(today.setHours(0, 0, 0, 0));
    const end = new Date(today.setHours(23, 59, 59, 999));
    return this.getAppointmentsByDateRange(salonId, start, end);
  }

  // ==============================
  // APPOINTMENT CREATION
  // ==============================
  
  async createAppointment(data) {
    // Check staff availability
    const available = await this.checkStaffAvailability(
      data.staffId,
      data.scheduledAt,
      data.duration || 60
    );
    if (!available) throw new Error("Staff not available at this time.");

    // Create appointment
    const appointment = new Appointment({
      ...data,
      status: "booked",
    });

    await appointment.save();
    this.sendNotification(appointment, "Appointment confirmed!");
    return appointment;
  }

  async updateAppointment(appointmentId, updateData, salonId) {
    if (updateData.scheduledAt && updateData.staffId) {
      const available = await this.checkStaffAvailability(
        updateData.staffId,
        updateData.scheduledAt,
        updateData.duration || 60,
        appointmentId
      );
      if (!available) throw new Error("Staff not available at this time.");
    }
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      updateData,
      { new: true }
    );
    this.sendNotification(appointment, "Appointment updated!");
    return appointment;
  }

  async updateAppointmentStatus(appointmentId, status, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status },
      { new: true }
    );
    this.sendNotification(appointment, `Status updated to ${status}`);
    return appointment;
  }

  async cancelAppointment(appointmentId, reason, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status: "cancelled", cancelReason: reason },
      { new: true }
    );
    this.sendNotification(appointment, `Appointment cancelled: ${reason}`);
    return appointment;
  }

  async deleteAppointment(appointmentId, salonId) {
    return await Appointment.findOneAndDelete({ _id: appointmentId, tenantId: salonId });
  }

  async handleNoShow(appointmentId, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status: "no-show" },
      { new: true }
    );
    this.sendNotification(appointment, "Customer no-show");
    return appointment;
  }

  // ==============================
  // STAFF AVAILABILITY
  // ==============================
  
  async checkStaffAvailability(staffId, scheduledAt, duration = 60, excludeAppointmentId = null) {
    const start = new Date(scheduledAt);
    const end = addMinutes(start, duration);

    const overlapping = await Appointment.findOne({
      staffId,
      _id: { $ne: excludeAppointmentId },
      scheduledAt: { $lt: end, $gte: start },
      status: { $in: ["booked", "in-progress"] },
    });

    return !overlapping;
  }

  async getAvailableTimeSlots(salonId, date, serviceDuration = 60) {
    const staffList = await Staff.find({ salonId });
    const slots = [];

    for (const staff of staffList) {
      let current = new Date(date.setHours(9, 0, 0, 0));
      const endOfDay = new Date(date.setHours(18, 0, 0, 0));

      while (current < endOfDay) {
        const available = await this.checkStaffAvailability(staff._id, current, serviceDuration);
        if (available) slots.push({ staffId: staff._id, time: new Date(current) });
        current = addMinutes(current, 30);
      }
    }
    return slots;
  }

  // ==============================
  // COMPREHENSIVE NOTIFICATION SYSTEM
  // ==============================

  async sendAppointmentConfirmation(appointmentId) {
    try {
      const appointment = await Appointment.findById(appointmentId)
        .populate('customerId')
        .populate('staffId')
        .populate('serviceIds');

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      const customer = appointment.customerId;
      const staff = appointment.staffId;
      const services = appointment.serviceIds;

      // Generate confirmation details
      const confirmationData = {
        appointmentId: appointment._id,
        confirmationNumber: this._generateConfirmationNumber(),
        customerName: customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        staffName: staff.name,
        services: services.map(s => s.name).join(', '),
        scheduledAt: appointment.scheduledAt,
        duration: appointment.duration,
        totalPrice: appointment.totalPrice,
        salonAddress: 'Your Salon Address', // Get from salon settings
        cancellationPolicy: 'Free cancellation up to 24 hours before appointment'
      };

      // Send email confirmation
      await this._sendEmailNotification(customer.email, 'appointment_confirmation', confirmationData);

      // Send SMS confirmation if phone number provided
      if (customer.phone) {
        await this._sendSMSNotification(customer.phone, 'appointment_confirmation', confirmationData);
      }

      // Notify staff
      await this._sendStaffNotification(staff.email, 'new_appointment', confirmationData);

      // Create calendar event
      const calendarEvent = this._generateCalendarEvent(confirmationData);

      logger.info('Appointment confirmation sent', { appointmentId, confirmationNumber: confirmationData.confirmationNumber });

      return {
        success: true,
        confirmationNumber: confirmationData.confirmationNumber,
        calendarEvent
      };
    } catch (error) {
      logger.error('Error sending appointment confirmation', error);
      throw error;
    }
  }

  async sendAppointmentReminders(appointmentId, reminderType = '24h') {
    try {
      const appointment = await Appointment.findById(appointmentId)
        .populate('customerId')
        .populate('staffId')
        .populate('serviceIds');

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      const customer = appointment.customerId;
      const reminderData = {
        customerName: customer.name,
        appointmentDate: format(appointment.scheduledAt, 'EEEE, MMMM do, yyyy'),
        appointmentTime: format(appointment.scheduledAt, 'h:mm a'),
        services: appointment.serviceIds.map(s => s.name).join(', '),
        staffName: appointment.staffId.name,
        preparationInstructions: this._getPreparationInstructions(appointment.serviceIds),
        confirmationLink: `${process.env.FRONTEND_URL}/appointments/${appointmentId}/confirm`,
        rescheduleLink: `${process.env.FRONTEND_URL}/appointments/${appointmentId}/reschedule`
      };

      let subject, template;
      switch (reminderType) {
        case '24h':
          subject = 'Appointment Reminder - Tomorrow';
          template = 'appointment_reminder_24h';
          break;
        case '2h':
          subject = 'Appointment Reminder - In 2 Hours';
          template = 'appointment_reminder_2h';
          break;
        case '30min':
          subject = 'Appointment Starting Soon';
          template = 'appointment_reminder_30min';
          break;
        default:
          subject = 'Appointment Reminder';
          template = 'appointment_reminder_default';
      }

      // Send email reminder
      await this._sendEmailNotification(customer.email, template, reminderData, subject);

      // Send SMS reminder for urgent reminders
      if (reminderType === '2h' || reminderType === '30min') {
        await this._sendSMSNotification(customer.phone, template, reminderData);
      }

      logger.info('Appointment reminder sent', { appointmentId, reminderType });

      return { success: true, reminderType, sentAt: new Date() };
    } catch (error) {
      logger.error('Error sending appointment reminder', error);
      throw error;
    }
  }

  async sendAppointmentUpdates(appointmentId, updateType, updateData = {}) {
    try {
      const appointment = await Appointment.findById(appointmentId)
        .populate('customerId')
        .populate('staffId')
        .populate('serviceIds');

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      const customer = appointment.customerId;
      const staff = appointment.staffId;

      const notificationData = {
        customerName: customer.name,
        appointmentId: appointment._id,
        originalDate: format(appointment.scheduledAt, 'EEEE, MMMM do, yyyy'),
        originalTime: format(appointment.scheduledAt, 'h:mm a'),
        staffName: staff.name,
        services: appointment.serviceIds.map(s => s.name).join(', '),
        ...updateData
      };

      let subject, template;
      switch (updateType) {
        case 'rescheduled':
          subject = 'Appointment Rescheduled';
          template = 'appointment_rescheduled';
          notificationData.newDate = format(updateData.newScheduledAt, 'EEEE, MMMM do, yyyy');
          notificationData.newTime = format(updateData.newScheduledAt, 'h:mm a');
          break;
        case 'cancelled':
          subject = 'Appointment Cancelled';
          template = 'appointment_cancelled';
          notificationData.reason = updateData.reason || 'No reason provided';
          notificationData.refundInfo = updateData.refundInfo || 'Refund will be processed within 3-5 business days';
          break;
        case 'staff_changed':
          subject = 'Staff Assignment Updated';
          template = 'appointment_staff_changed';
          notificationData.newStaffName = updateData.newStaffName;
          break;
        case 'service_modified':
          subject = 'Appointment Services Updated';
          template = 'appointment_services_modified';
          notificationData.newServices = updateData.newServices;
          notificationData.newTotalPrice = updateData.newTotalPrice;
          break;
        default:
          subject = 'Appointment Updated';
          template = 'appointment_updated';
      }

      // Send notifications to customer
      await this._sendEmailNotification(customer.email, template, notificationData, subject);
      if (customer.phone) {
        await this._sendSMSNotification(customer.phone, template, notificationData);
      }

      // Send notifications to staff
      await this._sendStaffNotification(staff.email, `staff_${template}`, notificationData);

      logger.info('Appointment update notification sent', { appointmentId, updateType });

      return { success: true, updateType, sentAt: new Date() };
    } catch (error) {
      logger.error('Error sending appointment update', error);
      throw error;
    }
  }

  // Helper methods for notifications
  async _sendEmailNotification(email, template, data, subject = null) {
    try {
      // This would integrate with your email service (SendGrid, AWS SES, etc.)
      logger.info('Sending email notification', { email, template, subject });

      // Placeholder for actual email sending logic
      const emailContent = this._generateEmailContent(template, data);

      // Example integration:
      // await emailService.send({
      //   to: email,
      //   subject: subject || this._getEmailSubject(template),
      //   html: emailContent
      // });

      return { success: true, email, template };
    } catch (error) {
      logger.error('Error sending email notification', error);
      throw error;
    }
  }

  async _sendSMSNotification(phone, template, data) {
    try {
      // This would integrate with your SMS service (Twilio, AWS SNS, etc.)
      logger.info('Sending SMS notification', { phone, template });

      const smsContent = this._generateSMSContent(template, data);

      // Example integration:
      // await smsService.send({
      //   to: phone,
      //   message: smsContent
      // });

      return { success: true, phone, template };
    } catch (error) {
      logger.error('Error sending SMS notification', error);
      throw error;
    }
  }

  async _sendStaffNotification(email, template, data) {
    try {
      logger.info('Sending staff notification', { email, template });

      const emailContent = this._generateStaffEmailContent(template, data);

      // Send to staff email
      return await this._sendEmailNotification(email, template, data);
    } catch (error) {
      logger.error('Error sending staff notification', error);
      throw error;
    }
  }

  _generateConfirmationNumber() {
    return 'CONF-' + Date.now().toString(36).toUpperCase() + Math.random().toString(36).substr(2, 5).toUpperCase();
  }

  _generateCalendarEvent(data) {
    return {
      title: `Appointment - ${data.services}`,
      start: data.scheduledAt,
      end: addMinutes(data.scheduledAt, data.duration),
      description: `Services: ${data.services}\nStaff: ${data.staffName}\nConfirmation: ${data.confirmationNumber}`,
      location: data.salonAddress
    };
  }

  _getPreparationInstructions(services) {
    // Return service-specific preparation instructions
    const instructions = [];
    services.forEach(service => {
      if (service.category === 'hair') {
        instructions.push('Please arrive with clean, dry hair');
      }
      if (service.category === 'nails') {
        instructions.push('Please remove any existing nail polish');
      }
      if (service.category === 'facial') {
        instructions.push('Please arrive with a clean face, no makeup');
      }
    });
    return instructions.length > 0 ? instructions.join('. ') : 'No special preparation required';
  }

  _generateEmailContent(template, data) {
    // Generate HTML email content based on template and data
    // This would typically use a template engine like Handlebars
    return `<html><body><h1>Email Template: ${template}</h1><pre>${JSON.stringify(data, null, 2)}</pre></body></html>`;
  }

  _generateSMSContent(template, data) {
    // Generate SMS content based on template and data
    switch (template) {
      case 'appointment_confirmation':
        return `Appointment confirmed for ${data.appointmentDate} at ${data.appointmentTime}. Confirmation: ${data.confirmationNumber}`;
      case 'appointment_reminder_24h':
        return `Reminder: Appointment tomorrow at ${data.appointmentTime} for ${data.services}`;
      case 'appointment_reminder_2h':
        return `Reminder: Appointment in 2 hours at ${data.appointmentTime}`;
      default:
        return `Appointment update: ${template}`;
    }
  }

  _generateStaffEmailContent(template, data) {
    // Generate staff-specific email content
    return this._generateEmailContent(template, data);
  }

  // Legacy method for backward compatibility
  sendNotification(appointment, message) {
    logger.warn('Using deprecated sendNotification method', { appointmentId: appointment._id, message });
    console.log(`Notification: ${message} for appointment ${appointment._id}`);
  }

  // ==============================
  // ANALYTICS
  // ==============================
  
  async getAppointmentAnalytics(salonId) {
    const totalAppointments = await Appointment.countDocuments({ tenantId: salonId });
    const completed = await Appointment.countDocuments({ tenantId: salonId, status: "completed" });
    const cancelled = await Appointment.countDocuments({ tenantId: salonId, status: "cancelled" });

    return { totalAppointments, completed, cancelled };
  }
}

// Export service instance
module.exports = new AppointmentService();
