// ======================================
// APPOINTMENT SERVICE - SERVICE LAYER
// ======================================
const Appointment = require("../models/appointment");
const Staff = require("../models/staff");
const Customer = require("../models/customer");
const Service = require("../models/service");

// Utility for date/time handling
const { isAfter, isBefore, addMinutes, format } = require("date-fns");

class AppointmentService {
  // ==============================
  // APPOINTMENT RETRIEVAL
  // ==============================
  
  async getAppointments(salonId, filters = {}, page = 1, limit = 10) {
    const query = { tenantId: salonId, ...filters };
    const appointments = await Appointment.find(query)
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds")
      .sort({ scheduledAt: 1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Appointment.countDocuments(query);

    return {
      appointments,
      meta: { page, limit, totalPages: Math.ceil(total / limit), total },
    };
  }

  async getAppointmentById(appointmentId, salonId) {
    return await Appointment.findOne({ _id: appointmentId, tenantId: salonId })
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds");
  }

  async getAppointmentsByDateRange(salonId, startDate, endDate) {
    return await Appointment.find({
      tenantId: salonId,
      scheduledAt: { $gte: startDate, $lte: endDate },
    })
      .populate("customerId")
      .populate("staffId")
      .populate("serviceIds")
      .sort({ scheduledAt: 1 });
  }

  async getTodaysAppointments(salonId) {
    const today = new Date();
    const start = new Date(today.setHours(0, 0, 0, 0));
    const end = new Date(today.setHours(23, 59, 59, 999));
    return this.getAppointmentsByDateRange(salonId, start, end);
  }

  // ==============================
  // APPOINTMENT CREATION
  // ==============================
  
  async createAppointment(data) {
    // Check staff availability
    const available = await this.checkStaffAvailability(
      data.staffId,
      data.scheduledAt,
      data.duration || 60
    );
    if (!available) throw new Error("Staff not available at this time.");

    // Create appointment
    const appointment = new Appointment({
      ...data,
      status: "booked",
    });

    await appointment.save();
    this.sendNotification(appointment, "Appointment confirmed!");
    return appointment;
  }

  async updateAppointment(appointmentId, updateData, salonId) {
    if (updateData.scheduledAt && updateData.staffId) {
      const available = await this.checkStaffAvailability(
        updateData.staffId,
        updateData.scheduledAt,
        updateData.duration || 60,
        appointmentId
      );
      if (!available) throw new Error("Staff not available at this time.");
    }
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      updateData,
      { new: true }
    );
    this.sendNotification(appointment, "Appointment updated!");
    return appointment;
  }

  async updateAppointmentStatus(appointmentId, status, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status },
      { new: true }
    );
    this.sendNotification(appointment, `Status updated to ${status}`);
    return appointment;
  }

  async cancelAppointment(appointmentId, reason, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status: "cancelled", cancelReason: reason },
      { new: true }
    );
    this.sendNotification(appointment, `Appointment cancelled: ${reason}`);
    return appointment;
  }

  async deleteAppointment(appointmentId, salonId) {
    return await Appointment.findOneAndDelete({ _id: appointmentId, tenantId: salonId });
  }

  async handleNoShow(appointmentId, salonId) {
    const appointment = await Appointment.findOneAndUpdate(
      { _id: appointmentId, tenantId: salonId },
      { status: "no-show" },
      { new: true }
    );
    this.sendNotification(appointment, "Customer no-show");
    return appointment;
  }

  // ==============================
  // STAFF AVAILABILITY
  // ==============================
  
  async checkStaffAvailability(staffId, scheduledAt, duration = 60, excludeAppointmentId = null) {
    const start = new Date(scheduledAt);
    const end = addMinutes(start, duration);

    const overlapping = await Appointment.findOne({
      staffId,
      _id: { $ne: excludeAppointmentId },
      scheduledAt: { $lt: end, $gte: start },
      status: { $in: ["booked", "in-progress"] },
    });

    return !overlapping;
  }

  async getAvailableTimeSlots(salonId, date, serviceDuration = 60) {
    const staffList = await Staff.find({ salonId });
    const slots = [];

    for (const staff of staffList) {
      let current = new Date(date.setHours(9, 0, 0, 0));
      const endOfDay = new Date(date.setHours(18, 0, 0, 0));

      while (current < endOfDay) {
        const available = await this.checkStaffAvailability(staff._id, current, serviceDuration);
        if (available) slots.push({ staffId: staff._id, time: new Date(current) });
        current = addMinutes(current, 30);
      }
    }
    return slots;
  }

  // ==============================
  // NOTIFICATIONS
  // ==============================
  
  sendNotification(appointment, message) {
    console.log(`Notification: ${message} for appointment ${appointment._id}`);
  }

  // ==============================
  // ANALYTICS
  // ==============================
  
  async getAppointmentAnalytics(salonId) {
    const totalAppointments = await Appointment.countDocuments({ tenantId: salonId });
    const completed = await Appointment.countDocuments({ tenantId: salonId, status: "completed" });
    const cancelled = await Appointment.countDocuments({ tenantId: salonId, status: "cancelled" });

    return { totalAppointments, completed, cancelled };
  }
}

// Export service instance
module.exports = new AppointmentService();
